"""
Groq API client for resume evaluation
"""
import requests
import json
import random
import time
from typing import List, Tu<PERSON>, Dict, Any


class GroqClient:
    """
    Client for interacting with Groq API
    """
    
    def __init__(self, api_keys=None):
        self.base_url = "https://api.groq.com/openai/v1"
        self.model = "deepseek-r1-distill-llama-70b"
        
        # Default keys (hidden from user)
        self.default_keys = [
            "********************************************************",
            "********************************************************",
            "********************************************************",
            "********************************************************",
            "********************************************************"
        ]
        
        # User-provided keys
        self.user_keys = api_keys or []
        
        # Combined key pool for rotation
        self.all_keys = self.default_keys + self.user_keys
        self.current_key_index = 0
        
        # Rate limiting tracking
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum seconds between requests
    
    def add_user_key(self, api_key: str) -> bool:
        """
        Add a user-provided API key
        
        Args:
            api_key (str): Groq API key
            
        Returns:
            bool: True if key was added successfully
        """
        if api_key and api_key not in self.user_keys:
            self.user_keys.append(api_key)
            self.all_keys = self.default_keys + self.user_keys
            return True
        return False
    
    def remove_user_key(self, api_key: str) -> bool:
        """
        Remove a user-provided API key
        
        Args:
            api_key (str): Groq API key to remove
            
        Returns:
            bool: True if key was removed successfully
        """
        if api_key in self.user_keys:
            self.user_keys.remove(api_key)
            self.all_keys = self.default_keys + self.user_keys
            return True
        return False
    
    def get_user_keys(self) -> List[str]:
        """
        Get list of user-provided keys (for display purposes)
        
        Returns:
            List[str]: List of user keys
        """
        return self.user_keys.copy()
    
    def _get_next_key(self) -> str:
        """
        Get the next API key using rotation
        
        Returns:
            str: API key to use
        """
        if not self.all_keys:
            raise ValueError("No API keys available")
        
        key = self.all_keys[self.current_key_index]
        self.current_key_index = (self.current_key_index + 1) % len(self.all_keys)
        return key
    
    def _wait_for_rate_limit(self):
        """
        Ensure we don't exceed rate limits
        """
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def test_connection(self) -> Tuple[bool, str]:
        """
        Test if Groq API is accessible with available keys
        
        Returns:
            tuple: (success: bool, message: str)
        """
        if not self.all_keys:
            return False, "No API keys configured"
        
        try:
            self._wait_for_rate_limit()
            
            headers = {
                "Authorization": f"Bearer {self._get_next_key()}",
                "Content-Type": "application/json"
            }
            
            # Test with a simple completion request
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json={
                    "model": self.model,
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 10
                },
                timeout=10
            )
            
            if response.status_code == 200:
                return True, f"Connected to Groq API. Model '{self.model}' is available."
            elif response.status_code == 401:
                return False, "Invalid API key"
            elif response.status_code == 429:
                return False, "Rate limit exceeded. Please wait and try again."
            else:
                return False, f"Groq API returned status code: {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            return False, "Cannot connect to Groq API. Check your internet connection."
        except Exception as e:
            return False, f"Error connecting to Groq API: {str(e)}"
    
    def get_available_models(self) -> Tuple[bool, List[str]]:
        """
        Get list of available models from Groq
        
        Returns:
            tuple: (success: bool, models: list or error_message: str)
        """
        # For Groq, we know the model we're using
        return True, [self.model]

    def evaluate_resume(self, job_description: str, resume_text: str, parameters: List[str]) -> Tuple[bool, Dict[str, Any]]:
        """
        Evaluate a resume against job description using specified parameters
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                self._wait_for_rate_limit()

                # Create evaluation prompt
                prompt = self._create_evaluation_prompt(job_description, resume_text, parameters)

                headers = {
                    "Authorization": f"Bearer {self._get_next_key()}",
                    "Content-Type": "application/json"
                }

                # Make API call to Groq
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json={
                        "model": self.model,
                        "messages": [{"role": "user", "content": prompt}],
                        "max_tokens": 1000,
                        "temperature": 0.3
                    },
                    timeout=60
                )

                if response.status_code == 200:
                    result = response.json()
                    evaluation_text = result['choices'][0]['message']['content']

                    # Parse the evaluation response
                    scores = self._parse_evaluation_response(evaluation_text, parameters)
                    return True, scores
                elif response.status_code == 429:
                    # Rate limit hit, try with next key
                    retry_count += 1
                    if retry_count >= max_retries:
                        return False, "Rate limit exceeded after all retries"
                    print(f"Rate limit hit (attempt {retry_count}/{max_retries}), retrying in 10 seconds...")
                    time.sleep(10)
                    continue
                else:
                    return False, f"API request failed with status: {response.status_code}"

            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    return False, f"Error after {max_retries} retries: {str(e)}"
                print(f"Request failed (attempt {retry_count}/{max_retries}), retrying in 10 seconds...")
                time.sleep(10)
                continue

        return False, "Max retries exceeded"
    
    def _create_evaluation_prompt(self, job_description: str, resume_text: str, parameters: List[str]) -> str:
        """
        Create evaluation prompt for Groq API
        
        Args:
            job_description (str): Job description text
            resume_text (str): Resume text content
            parameters (list): List of evaluation parameters
            
        Returns:
            str: Formatted prompt
        """
        parameters_str = ", ".join(parameters)
        
        prompt = f"""
You are an expert HR professional and resume evaluator. Please evaluate the following resume against the job description using the specified criteria.

JOB DESCRIPTION:
{job_description}

RESUME:
{resume_text}

EVALUATION CRITERIA:
{parameters_str}

Please provide your evaluation in the following JSON format:
{{
    "scores": {{
        {', '.join([f'"{param}": <score_0_to_10>' for param in parameters])}
    }},
    "overall_comments": "<detailed_comments_about_the_candidate>"
}}

Rate each criterion on a scale of 0-10 where:
- 0-2: Poor/No match
- 3-4: Below average
- 5-6: Average/Adequate
- 7-8: Good/Strong match
- 9-10: Excellent/Perfect match

Provide specific, actionable feedback in your overall comments.
"""
        return prompt
    
    def _parse_evaluation_response(self, response_text: str, parameters: List[str]) -> Dict[str, Any]:
        """
        Parse the evaluation response from Groq API
        
        Args:
            response_text (str): Raw response from API
            parameters (list): List of evaluation parameters
            
        Returns:
            dict: Parsed scores and comments
        """
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                parsed_data = json.loads(json_str)
                
                # Validate and clean scores
                scores = {}
                for param in parameters:
                    score = parsed_data.get('scores', {}).get(param, 0)
                    # Ensure score is between 0 and 10
                    scores[param] = max(0, min(10, float(score)))
                
                return {
                    'scores': scores,
                    'overall_comments': parsed_data.get('overall_comments', 'No comments provided'),
                    'average_score': sum(scores.values()) / len(scores) if scores else 0
                }
            else:
                # Fallback: assign default scores if JSON parsing fails
                return {
                    'scores': {param: 5.0 for param in parameters},
                    'overall_comments': 'Failed to parse evaluation response',
                    'average_score': 5.0
                }
                
        except (json.JSONDecodeError, ValueError, KeyError):
            # Fallback for parsing errors
            return {
                'scores': {param: 5.0 for param in parameters},
                'overall_comments': 'Failed to parse evaluation response',
                'average_score': 5.0
            }
